import hashlib
import os
import re
import time
import tempfile
import shutil
import requests
import random
import cv2
import subprocess
from typing import Optional, Tuple
import threading
import queue

from task_manager import video_task_manager
from oss_utils import oss_manager
import service.trans_dh_service

from h_utils.custom import CustomError
from y_utils.config import GlobalConfig
from y_utils.logger import logger
# Configure logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# logger = logging.getLogger("video_synthesis")

# Constants
MODELS_BASE_DIR = "models"
TEMP_DIR = "temp"
# 文件名标准化函数
def sanitize_filename(filename):
    """
    标准化文件名，移除特殊字符并保留扩展名。
    """
    # 分离文件名和扩展名
    name, ext = os.path.splitext(filename)
    # 使用正则表达式替换特殊字符为下划线
    sanitized_name = re.sub(r"[^a-zA-Z0-9_-]", "_", name)
    # 返回标准化后的文件名
    return f"{sanitized_name}{ext}"

def get_subtitle_color_style(work_id: str) -> str:
    """
    根据work_id查询数据库获取字幕颜色配置

    Args:
        work_id: 任务ID

    Returns:
        str: 字幕颜色样式字符串，如果不需要颜色则返回空字符串
    """
    try:
        from database import get_db_session, Task
        db = get_db_session()
        try:
            task = db.query(Task).filter(Task.task_uuid == work_id).first()
            if task and task.subtitle_option:
                if task.subtitle_option == '黑':
                    return ":force_style='FontName=WenQuanYi Zen Hei,PrimaryColour=&H000000&'"
                elif task.subtitle_option == '白':
                    return ":force_style='FontName=WenQuanYi Zen Hei,PrimaryColour=&HFFFFFF&'"
                else:  # '无' 或其他值
                    return ":force_style='FontName=WenQuanYi Zen Hei'"
            else:
                return ":force_style='FontName=WenQuanYi Zen Hei'"
        finally:
            db.close()
    except Exception as e:
        logger.error(f"获取字幕颜色配置失败: {str(e)}")
        # 默认返回只有字体的配置
        return ":force_style='FontName=WenQuanYi Zen Hei'"
def write_video(
    output_imgs_queue,
    temp_dir,
    result_dir,
    work_id,
    audio_path,
    result_queue,
    width,
    height,
    fps,
    watermark_switch=0,
    digital_auth=0,
):
    #fps=25
    output_mp4 = os.path.join("./temp", "{}.mp4".format(work_id))
    result_path = os.path.join("./result", "{}.mp4".format(work_id))

    # Ensure output directories exist
    os.makedirs(os.path.dirname(output_mp4), exist_ok=True)
    os.makedirs(os.path.dirname(result_path), exist_ok=True)

    # Try different codecs in order of preference for compatibility
    codecs_to_try = [
        cv2.VideoWriter_fourcc(*"mp4v"),  # MPEG-4 Part 2 (most compatible)
        cv2.VideoWriter_fourcc(*"XVID"),  # Xvid MPEG-4
        cv2.VideoWriter_fourcc(*"MJPG"),  # Motion JPEG (fallback)
        cv2.VideoWriter_fourcc(*"X264"),  # H.264 alternative
    ]

    video_write = None
    for fourcc in codecs_to_try:
        video_write = cv2.VideoWriter(output_mp4, fourcc, fps, (width, height))
        if video_write.isOpened():
            logger.info(f"Successfully initialized VideoWriter with fourcc: {fourcc}")
            break
        else:
            video_write.release()

    try:
        if video_write is None or not video_write.isOpened():
            logger.warning(f"OpenCV VideoWriter failed for {work_id}, falling back to FFmpeg-based approach")
            # Fallback: save frames as images and use FFmpeg to create video
            frames_dir = os.path.join("./temp", f"{work_id}_frames")
            os.makedirs(frames_dir, exist_ok=True)

            frame_count = 0
            while True:
                state, reason, value_ = output_imgs_queue.get()
                if type(state) == bool and state == True:
                    logger.info(f"Custom VideoWriter [{work_id}]视频帧队列处理已结束")
                    break
                else:
                    if type(state) == bool and state == False:
                        logger.error(f"Custom VideoWriter [{work_id}]任务视频帧队列 -> 异常原因:[{reason}]")
                        raise CustomError(reason)

                    # Save frames as images
                    for result_img in value_:
                        frame_path = os.path.join(frames_dir, f"frame_{frame_count:06d}.jpg")
                        cv2.imwrite(frame_path, result_img)
                        frame_count += 1
                    logger.info(f"更新任务状态 任务id:{work_id}")
                    video_task_manager.update_task(
                        work_id,
                        status='processing',
                        code=302,
                        title='合成中',
                        msg='合成中',
                        time=str(int(time.time()))
                    )

            # Create video from frames using FFmpeg
            if frame_count > 0:
                ffmpeg_cmd = f"ffmpeg -loglevel warning -y -r {fps} -i {frames_dir}/frame_%06d.jpg -c:v libx264 -preset fast -crf 18 -pix_fmt yuv420p {output_mp4}"
                logger.info(f"Creating video from frames: {ffmpeg_cmd}")
                result = subprocess.run(ffmpeg_cmd, shell=True, check=True, capture_output=True, text=True)
                logger.info("Successfully created video from frames")

                # Clean up frame files
                import shutil
                shutil.rmtree(frames_dir, ignore_errors=True)
            else:
                logger.error(f"No frames to process for {work_id}")
                result_queue.put([False, f"没有帧数据可处理"])
                return
        else:
            print("Custom VideoWriter init done")
            frame_buffer = []
            buffer_size = 10  # Buffer frames for batch writing

            while True:
                state, reason, value_ = output_imgs_queue.get()
                if type(state) == bool and state == True:
                    logger.info(
                        "Custom VideoWriter [{}]视频帧队列处理已结束".format(work_id)
                    )

                    # Write any remaining frames in buffer
                    if frame_buffer:
                        for buffered_frame in frame_buffer:
                            video_write.write(buffered_frame)
                        frame_buffer.clear()

                    logger.info(
                        "Custom VideoWriter Silence Video saved in {}".format(
                            os.path.realpath(output_mp4)
                        )
                    )
                    break
                else:
                    if type(state) == bool and state == False:
                        logger.error(
                            "Custom VideoWriter [{}]任务视频帧队列 -> 异常原因:[{}]".format(
                                work_id, reason
                            )
                        )
                        raise CustomError(reason)

                    # Add frames to buffer for batch processing
                    for result_img in value_:
                        frame_buffer.append(result_img)

                        # Write buffer when it reaches the buffer size
                        if len(frame_buffer) >= buffer_size:
                            for buffered_frame in frame_buffer:
                                video_write.write(buffered_frame)
                            frame_buffer.clear()
                    logger.info(f"更新任务状态 任务id:{work_id}")
                    video_task_manager.update_task(
                        work_id,
                        status='processing',
                        code=302,
                        title='合成中',
                        msg='合成中',
                        time=str(int(time.time()))
                    )
            # Ensure video writer is properly released
            if video_write is not None and video_write.isOpened():
                video_write.release()

        # Optimize FFmpeg commands for better performance
        if watermark_switch == 1 and digital_auth == 1:
            logger.info(
                "Custom VideoWriter [{}]任务需要水印和数字人标识".format(work_id)
            )
            if width > height:
                command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                    audio_path,
                    output_mp4,
                    GlobalConfig.instance().watermark_path,
                    GlobalConfig.instance().digital_auth_path,
                    result_path,
                )
                logger.info("command:{}".format(command))
            else:
                command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                    audio_path,
                    output_mp4,
                    GlobalConfig.instance().watermark_path,
                    GlobalConfig.instance().digital_auth_path,
                    result_path,
                )
                logger.info("command:{}".format(command))
        elif watermark_switch == 1 and digital_auth == 0:
            logger.info("Custom VideoWriter [{}]任务需要水印".format(work_id))
            command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                audio_path,
                output_mp4,
                GlobalConfig.instance().watermark_path,
                result_path,
            )
            logger.info("command:{}".format(command))
        elif watermark_switch == 0 and digital_auth == 1:
            logger.info("Custom VideoWriter [{}]任务需要数字人标识".format(work_id))
            if width > height:
                command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                    audio_path,
                    output_mp4,
                    GlobalConfig.instance().digital_auth_path,
                    result_path,
                )
                logger.info("command:{}".format(command))
            else:
                command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                    audio_path,
                    output_mp4,
                    GlobalConfig.instance().digital_auth_path,
                    result_path,
                )
                logger.info("command:{}".format(command))
        else:
            command = "ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}".format(
                audio_path, output_mp4, result_path
            )
            logger.info("Custom command:{}".format(command))

        # Use subprocess.run for better error handling and performance
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"FFmpeg completed successfully: {result.stdout}")

        print("###### Custom Video Writer write over")
        print(f"###### Video result saved in {os.path.realpath(result_path)}")
        result_queue.put([True, result_path])

    except subprocess.CalledProcessError as e:
        logger.error(f"FFmpeg failed: {e.stderr}")
        result_queue.put([False, f"FFmpeg处理失败: {e.stderr}"])
    except Exception as e:
        logger.error(
            "Custom VideoWriter [{}]视频帧队列处理异常结束，异常原因:[{}]".format(
                work_id, e.__str__()
            )
        )
        result_queue.put(
            [
                False,
                "[{}]视频帧队列处理异常结束，异常原因:[{}]".format(
                    work_id, e.__str__()
                ),
            ]
        )
    finally:
        logger.info("Custom VideoWriter 后处理进程结束")

service.trans_dh_service.write_video = write_video
class VideoSynthesizer:
    """
    Processor for handling video synthesis tasks.
    """

    def __init__(self):
        """Initialize the video synthesizer."""
        self.task = service.trans_dh_service.TransDhTask()
        # Create necessary directories
        os.makedirs(MODELS_BASE_DIR, exist_ok=True)
        os.makedirs(TEMP_DIR, exist_ok=True)

        # Initialize task queue and worker threads
        self.task_queue = queue.Queue()
        self.worker_threads = []
        self.max_workers = 3  # Maximum number of concurrent synthesis tasks
        self.start_worker_threads()


    def process_video(
        self, audio_file, video_file, watermark=False, digital_auth=False, task_id=None
    ):
        code = task_id
        temp_dir = os.path.join(GlobalConfig.instance().temp_dir, code)
        result_dir = GlobalConfig.instance().result_dir
        os.makedirs(temp_dir, exist_ok=True)

        try:

            cap = cv2.VideoCapture(video_file)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = 25
            cap.release()

            self.task.task_dic[code] = f"{result_dir}/{code}"
            self.task.work(audio_file, video_file, code, 0, 0, 0, 0)
            logger.info(f"{code} 任务处理返回结果{self.task.task_dic[code]}")
            msg = self.task.task_dic[code][3]
            

            output_video_path= f"result/{code}.mp4"
            return output_video_path, msg
        except Exception as e:
            logger.error(f"处理视频时发生错误: {e}")
            return None, str(e)  # 出错时也返回两个值，msg 是异常信息


    def start_worker_threads(self):
        """Start worker threads for processing synthesis tasks."""
        for i in range(self.max_workers):
            thread = threading.Thread(target=self.worker_loop, daemon=True)
            thread.start()
            self.worker_threads.append(thread)

    def worker_loop(self):
        """Worker loop for processing synthesis tasks."""
        while True:
            try:
                task_id, audio_path, model_id, callback_url = self.task_queue.get()
                self.process_synthesis_task(task_id, audio_path, model_id, callback_url)
            except Exception as e:
                logger.error(f"Error in worker thread: {str(e)}")
            finally:
                self.task_queue.task_done()

    async def download_audio(self, url: str, output_path: str) -> bool:
        """
        Download an audio file from a URL.

        Args:
            url: URL of the audio to download
            output_path: Path to save the downloaded audio

        Returns:
            True if download was successful, False otherwise
        """
        try:
            # Create a temporary file for downloading
            with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
                temp_path = temp_file.name

            # Download the audio
            response = requests.get(url, stream=True, timeout=60)
            if response.status_code != 200:
                logger.error(f"Failed to download audio, status code: {response.status_code}")
                return False

            # Save the audio to the temporary file
            with open(temp_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            # Move the temporary file to the output path
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            shutil.move(temp_path, output_path)

            logger.info(f"Successfully downloaded audio to {output_path}")
            return True
        except Exception as e:
            logger.error(f"Error downloading audio: {str(e)}")
            # Clean up temporary file if it exists
            if 'temp_path' in locals() and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            return False

    def extract_video_frame(self, video_path: str, output_path: str, frame_number: Optional[int] = None) -> bool:
        """
        Extract a frame from a video.

        Args:
            video_path: Path to the video file
            output_path: Path to save the extracted frame
            frame_number: Frame number to extract (random if None)

        Returns:
            True if extraction was successful, False otherwise
        """
        try:
            # Open the video file
            cap = cv2.VideoCapture(video_path)

            # Check if video opened successfully
            if not cap.isOpened():
                logger.error(f"Failed to open video file: {video_path}")
                return False

            # Get total number of frames
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # If frame_number is None, choose a random frame
            if frame_number is None:
                frame_number = random.randint(0, total_frames - 1)

            # If requested frame is beyond total frames, use the middle frame
            if frame_number >= total_frames:
                frame_number = total_frames // 2

            # Set position to the requested frame
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)

            # Read the frame
            ret, frame = cap.read()

            # Release the video capture object
            cap.release()

            if not ret:
                logger.error(f"Failed to read frame {frame_number} from video")
                return False

            # Save the frame as an image
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            cv2.imwrite(output_path, frame)

            logger.info(f"Successfully extracted frame to {output_path}")
            return True
        except Exception as e:
            logger.error(f"Error extracting video frame: {str(e)}")
            return False

    def synthesize_video(self, audio_path: str, video_path: str, output_path: str) -> bool:
        """
        Synthesize a video using the audio and reference video.

        Args:
            audio_path: Path to the audio file
            video_path: Path to the reference video file
            output_path: Path to save the synthesized video

        Returns:
            True if synthesis was successful, False otherwise
        """
        try:
            # Use ffmpeg to combine audio and video
            command = f"ffmpeg -loglevel warning -y -i {audio_path} -i {video_path} -c:a aac -c:v libx264 -crf 15 -strict -2 {output_path}"
            logger.info(f"Running command: {command}")

            # Run the command
            result = subprocess.run(command, shell=True, check=True)

            if result.returncode == 0:
                logger.info(f"Successfully synthesized video to {output_path}")
                return True
            else:
                logger.error(f"Failed to synthesize video, return code: {result.returncode}")
                return False
        except Exception as e:
            logger.error(f"Error synthesizing video: {str(e)}")
            return False

    def upload_to_oss(self, local_file_path: str, task_id: str, is_video: bool = True) -> Tuple[bool, Optional[str]]:
        """
        Upload a file to Aliyun OSS.

        Args:
            local_file_path: Path to the local file
            task_id: Task ID to use in the file name
            is_video: Whether the file is a video (True) or image (False)

        Returns:
            Tuple of (success, url)
        """
        try:
            if is_video:
                # For videos, use the video upload method
                success, url = oss_manager.upload_video(local_file_path, task_id)
                return success, url
            else:
                # For images, use the existing upload_file method
                success, url = oss_manager.upload_file(local_file_path, task_id)
                return success, url
        except Exception as e:
            logger.error(f"Error uploading file to OSS: {str(e)}")
            return False, None

    async def queue_synthesis_task(self, task_id: str, audio_url: str, model_id: str, callback_url: Optional[str] = None) -> None:
        """
        Queue a video synthesis task.

        Args:
            task_id: Task ID
            audio_url: URL of the audio to process
            model_id: Model ID for the reference video
            callback_url: Optional callback URL
        """
        # Create task directory
        task_dir = os.path.join(TEMP_DIR, task_id)
        os.makedirs(task_dir, exist_ok=True)

        # Update task status to processing
        video_task_manager.update_task(
            task_id,
            status='processing',
            model_id=model_id
        )

        try:
            # Download audio
            audio_path = os.path.join(task_dir, "audio.wav")
            download_success = await self.download_audio(audio_url, audio_path)

            if not download_success:
                logger.error(f"Failed to download audio for task {task_id}")
                video_task_manager.mark_task_failed(task_id, "音频下载失败")
                return

            # Add task to queue for processing
            self.task_queue.put((task_id, audio_path, model_id, callback_url))

        except Exception as e:
            logger.error(f"Error queueing task {task_id}: {str(e)}")
            video_task_manager.mark_task_failed(task_id, f"处理失败: {str(e)}")

    def process_synthesis_task(self, task_id: str, audio_path: str, model_id: str, callback_url: Optional[str] = None) -> None:
        """
        Process a video synthesis task.

        Args:
            task_id: Task ID
            audio_path: Path to the downloaded audio file
            model_id: Model ID for the reference video
            callback_url: Optional callback URL
        """
        task_dir = os.path.join(TEMP_DIR, task_id)

        try:
            # Check if model exists
            model_dir = os.path.join(MODELS_BASE_DIR, model_id)
            model_video_path = os.path.join(model_dir, "source.mp4")

            if not os.path.exists(model_video_path):
                logger.error(f"Model video not found for model ID {model_id}")
                video_task_manager.update_task(
                    task_id,
                    status='failed',
                    code=300,
                    title='模型id不存在',
                    msg='模型id不存在',
                    time=str(int(time.time()))
                )
                return

            # 调用复用的 process_video 方法进行合成
            output_video_path, msg  = self.process_video(
                audio_file=audio_path,
                video_file=model_video_path,
                watermark=False,      # 是否加水印
                digital_auth=False,    # 是否加数字人认证标识
                task_id =task_id
            )
            # 判断是否处理成功
            if output_video_path is None or msg != '任务完成':
                video_task_manager.mark_task_failed(task_id, msg, code=403)
                # Call callback URL if provided
                if callback_url:
                    self.call_callback(task_id, callback_url)
                return
            
            video_upload_success, video_url = self.upload_to_oss(output_video_path, task_id, is_video=True)

            if not video_upload_success or not video_url:
                logger.error(f"Failed to upload video to OSS for task {task_id}")
                video_task_manager.mark_task_failed(task_id, "视频上传失败",code = 403)
                # Call callback URL if provided
                if callback_url:
                    self.call_callback(task_id, callback_url)
                return

            # Upload thumbnail to OSS
            img_url = None
            # if os.path.exists(thumbnail_path):
            #     thumbnail_upload_success, img_url = self.upload_to_oss(thumbnail_path, task_id, is_video=False)

            #     if not thumbnail_upload_success:
            #         logger.warning(f"Failed to upload thumbnail to OSS for task {task_id}")
                    # Continue anyway, this is not critical

            model_dir = os.path.join(MODELS_BASE_DIR, model_id)
            with open(os.path.join(model_dir, "thumbnail.txt"), "r") as f:
                img_url = f.read()

            # Get audio duration instead of processing time
            audio_duration = self.get_audio_duration(audio_path)
            # Mark task as completed
            video_task_manager.mark_task_completed(
                task_id,
                video_url,
                img_url,
                md5=self.calculate_md5(output_video_path),
                seconds=audio_duration
            )
            # Call callback URL if provided
            if callback_url:
                self.call_callback(task_id, callback_url)
            


            logger.info(f"Successfully processed synthesis task {task_id}")
        except Exception as e:
            logger.error(f"Error processing synthesis task {task_id}: {str(e)}")
            video_task_manager.mark_task_failed(task_id, f"处理失败: {str(e)}")
        finally:
        # 清理文件 清理根目录以 task_id开头的目录和文件
            try:
                for root, dirs, files in os.walk("."):
                    for file in files:
                        if file.startswith(task_id):
                            os.remove(os.path.join(root, file))
                    for dir in dirs:
                        if dir.startswith(task_id):
                            shutil.rmtree(os.path.join(root, dir))
            except Exception as e:
                logger.error(f"Failed to clean up task {task_id}: {str(e)}")

    def get_audio_duration(self, audio_path: str) -> int:
        """
        Get the duration of an audio file in seconds.

        Args:
            audio_path: Path to the audio file

        Returns:
            Duration in seconds (rounded to integer)
        """
        try:
            # Use ffprobe to get audio duration
            command = f'ffprobe -v quiet -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{audio_path}"'
            result = subprocess.run(command, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                duration = float(result.stdout.strip())
                return int(round(duration))
            else:
                logger.error(f"Failed to get audio duration using ffprobe: {result.stderr}")
                return 0
        except Exception as e:
            logger.error(f"Error getting audio duration: {str(e)}")
            return 0

    def calculate_md5( self,file_path: str, chunk_size=8192):
        with open(file_path, "rb") as f:
            md5_hash = hashlib.md5()
            while chunk := f.read(8192):
                md5_hash.update(chunk)
        return md5_hash.hexdigest()

    def call_callback(self, task_id: str, callback_url: str) -> bool:
        """
        Call the callback URL for a completed task.

        Args:
            task_id: Task ID
            callback_url: Callback URL

        Returns:
            True if callback was successful, False otherwise
        """
        try:
            # Get task data
            task_data = video_task_manager.get_task(task_id)
            if not task_data:
                logger.error(f"Task {task_id} not found for callback")
                return False

            # Prepare callback data
            callback_data = {
                "code": task_data.get("code", 200),
                "title": task_data.get("title", "合成完成"),
                "msg": task_data.get("msg", "ok"),
                "time": task_data.get("time", str(int(time.time()))),
                "task_id": task_id,
                "oss_url": task_data.get("oss_url", ""),
                "img_url": task_data.get("img_url", ""),
                "jg": task_data.get("jg", "yes"),
                "seconds": task_data.get("seconds"),
                "md5": task_data.get("md5")
            }

            # Send POST request to callback URL
            response = requests.post(callback_url, json=callback_data, timeout=30)

            if response.status_code == 200:
                logger.info(f"Successfully called callback URL for task {task_id}")
                return True
            else:
                logger.error(f"Failed to call callback URL, status code: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Error calling callback URL for task {task_id}: {str(e)}")
            return False


