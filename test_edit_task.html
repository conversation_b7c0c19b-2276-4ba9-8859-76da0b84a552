<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑任务功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
        code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🎯 编辑任务功能测试指南</h1>
    
    <div class="test-section">
        <h2>✅ 已完成的功能</h2>
        <ul>
            <li><strong>编辑任务模态框增强</strong>：添加了音频模型和视频形象选择功能</li>
            <li><strong>预览图功能</strong>：支持视频形象预览图显示</li>
            <li><strong>预览图清空</strong>：打开编辑对话框时自动清空预览图</li>
            <li><strong>模态框关闭清理</strong>：关闭模态框时清空预览图</li>
            <li><strong>后端API更新</strong>：支持更新任务的模型信息</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🧪 测试步骤</h2>
        <ol>
            <li><strong>访问用户任务页面</strong>
                <br><code>http://localhost:8000/user/tasks</code>
            </li>
            
            <li><strong>创建测试任务</strong>
                <ul>
                    <li>点击"新建任务"按钮</li>
                    <li>填写任务名称</li>
                    <li>选择音频模型和视频形象</li>
                    <li>观察预览图是否正常显示</li>
                    <li>创建任务</li>
                </ul>
            </li>
            
            <li><strong>测试编辑功能</strong>
                <ul>
                    <li>点击任务列表中的"编辑"按钮（铅笔图标）</li>
                    <li>验证编辑对话框是否包含：
                        <ul>
                            <li>任务名称输入框</li>
                            <li>音频模型选择下拉框</li>
                            <li>视频形象选择下拉框</li>
                            <li>文案内容输入框</li>
                            <li>字幕选项下拉框</li>
                        </ul>
                    </li>
                    <li>验证当前任务的模型选择是否正确设置</li>
                    <li>验证预览图是否正确显示（如果有）</li>
                </ul>
            </li>
            
            <li><strong>测试预览图清空功能</strong>
                <ul>
                    <li>打开编辑对话框</li>
                    <li>验证预览图区域是否为空（隐藏状态）</li>
                    <li>选择不同的视频形象</li>
                    <li>验证预览图是否正确更新</li>
                    <li>关闭对话框再重新打开</li>
                    <li>验证预览图是否重新清空</li>
                </ul>
            </li>
            
            <li><strong>测试保存功能</strong>
                <ul>
                    <li>修改任务信息（名称、模型、内容等）</li>
                    <li>点击"保存"按钮</li>
                    <li>验证任务是否成功更新</li>
                    <li>验证预览图是否在保存后清空</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔍 预期行为</h2>
        <ul>
            <li><span class="success">✓</span> 编辑对话框包含完整的任务编辑功能</li>
            <li><span class="success">✓</span> 打开编辑对话框时预览图为空</li>
            <li><span class="success">✓</span> 选择视频形象时显示对应预览图</li>
            <li><span class="success">✓</span> 关闭对话框时清空预览图</li>
            <li><span class="success">✓</span> 保存任务后清空预览图</li>
            <li><span class="success">✓</span> 任务信息正确更新到数据库</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🐛 可能的问题</h2>
        <ul>
            <li><span class="warning">⚠</span> 如果模型数据未加载，编辑对话框可能显示空的下拉框</li>
            <li><span class="warning">⚠</span> 预览图URL可能不存在，导致图片无法显示</li>
            <li><span class="error">✗</span> 网络错误可能导致保存失败</li>
            <li><span class="error">✗</span> 权限问题可能导致无法编辑某些任务</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>📝 技术实现要点</h2>
        <ul>
            <li><strong>前端改动</strong>：
                <ul>
                    <li>编辑模态框添加了模型选择功能</li>
                    <li>添加了 <code>showEditTaskVideoModelPreview()</code> 函数</li>
                    <li>修改了 <code>editTask()</code> 函数以支持模型设置</li>
                    <li>添加了模态框关闭事件监听器</li>
                </ul>
            </li>
            <li><strong>后端改动</strong>：
                <ul>
                    <li>更新了 <code>PUT /api/tasks/{task_id}</code> API</li>
                    <li>支持更新 <code>model_uuid</code> 和 <code>video_model_uuid</code></li>
                </ul>
            </li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🎉 完成状态</h2>
        <p class="success">
            ✅ <strong>编辑任务功能已完全实现</strong><br>
            现在用户可以编辑任务的所有信息，包括音频模型和视频形象选择，
            并且预览图会在打开对话框时自动清空，提供更好的用户体验。
        </p>
    </div>
</body>
</html>
