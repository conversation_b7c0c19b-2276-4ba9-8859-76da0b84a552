import os
import sys
from fastapi import FastAPI, Depends, HTTPException, status, Response
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from database import get_db_session, Model, Task, VideoModel

# 确保可以导入父目录中的模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from auth import check_model_permission, check_task_permission, require_login
from database import get_video_model_by_uuid

# 新建一个 FastAPI 实例用于下载服务
app = FastAPI(title="Download Service", version="1.0.0")

def get_db_with_pool():
    """获取数据库会话（使用线程池管理）"""
    db = get_db_session()
    try:
        yield db
    finally:
        db.close()

@app.get("/models/{model_uuid}")
def download_model(model_uuid: str, db: Session = Depends(get_db_with_pool)):
    """模型下载接口"""
    model = db.query(Model).filter(Model.model_uuid == model_uuid).first()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )

    audio_dir = os.path.join("audio", model_uuid)
    if not os.path.exists(audio_dir):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型文件不存在"
        )

    audio_file = None
    for file in os.listdir(audio_dir):
        if file.startswith("audio."):
            audio_file = file
            break

    if not audio_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="音频文件不存在"
        )

    file_path = os.path.join(audio_dir, audio_file)
    file_extension = os.path.splitext(audio_file)[1]
    filename = f"{model_uuid}{file_extension}"

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/octet-stream'
    )

@app.get("/video/{task_uuid}")
def download_video(task_uuid: str, db: Session = Depends(get_db_with_pool)):
    """视频下载接口"""
    task = db.query(Task).filter(Task.task_uuid == task_uuid).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    video_file_path = os.path.join("result", f"{task_uuid}.mp4")

    if not os.path.exists(video_file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频文件不存在"
        )

    filename = f"{task_uuid}.mp4"

    return FileResponse(
        path=video_file_path,
        filename=filename,
        media_type='application/octet-stream'
    )

@app.get("/video_models/{model_uuid}")
def download_video_model(model_uuid: str, db: Session = Depends(get_db_with_pool)):
    """视频形象下载接口"""
    video_model = get_video_model_by_uuid(db, model_uuid)
    if not video_model:
        raise HTTPException(status_code=404, detail="视频形象不存在")

    if video_model.train_status != "completed":
        raise HTTPException(status_code=400, detail="视频形象训练未完成")

    video_dir = os.path.join("videos", model_uuid)
    if not os.path.exists(video_dir):
        raise HTTPException(status_code=404, detail="视频文件不存在")

    source_files = [f for f in os.listdir(video_dir) if f.startswith("source.")]
    if not source_files:
        raise HTTPException(status_code=404, detail="源视频文件不存在")

    video_path = os.path.join(video_dir, source_files[0])

    if not os.path.exists(video_path):
        raise HTTPException(status_code=404, detail="视频文件不存在")

    return FileResponse(
        video_path,
        media_type="video/mp4",
        filename=f"{video_model.model_name}.mp4"
    )

if __name__ == "__main__":
    import uvicorn
    # 单独启动下载服务，端口可以自定义，例如8881
    uvicorn.run(app, host="0.0.0.0", port=8881)