{% extends "base.html" %}

{% block title %}模型管理 - HeyGem 管理系统{% endblock %}

{% block page_title %}模型管理{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索模型名称或用户...">
                    <button class="btn btn-outline-secondary" type="button" onclick="searchModels()">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <select class="form-select" id="statusFilter" onchange="filterModels()">
                    <option value="">全部状态</option>
                    <option value="new">新建</option>
                    <option value="training">训练中</option>
                    <option value="completed">已完成</option>
                    <option value="failed">失败</option>
                </select>
            </div>
            <div class="col-md-4">
                <button type="button" class="btn btn-success" onclick="refreshModels()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>模型名称</th>
                        <th>模型UUID</th>
                        <th>所属用户</th>
                        <th>训练状态</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="modelsTable">
                    <tr>
                        <td colspan="8" class="text-center">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页组件 -->
        <nav aria-label="模型列表分页" class="mt-3">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- Model Details Modal -->
<div class="modal fade" id="modelDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">模型详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="modelDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allModels = [];
let filteredModels = [];
let currentPage = 1;
const pageSize = 50;
let totalPages = 1;
let totalModels = 0;
let searchTerm = '';
let statusFilter = '';

document.addEventListener('DOMContentLoaded', function() {
    loadModels();

    // Search input event
    document.getElementById('searchInput').addEventListener('input', handleSearch);

    // Refresh data every 30 seconds
    setInterval(() => loadModels(currentPage), 30000);
});

async function loadModels(page = 1, noCache = false) {
    try {
        const skip = (page - 1) * pageSize;
        let url = `/api/models?skip=${skip}&limit=${pageSize}`;
        if (noCache) {
            url += '&no_cache=true';
        }

        const response = await axios.get(url);
        if (response.data.code === 200) {
            allModels = response.data.data;
            if (response.data.pagination) {
                totalModels = response.data.pagination.total;
                totalPages = Math.ceil(totalModels / pageSize);
            }
            currentPage = page;
            applyFilters();
            updatePagination();
        }
    } catch (error) {
        console.error('Error loading models:', error);
        showAlert('加载模型列表失败', 'danger');
    }
}

function renderModelsTable() {
    const tbody = document.getElementById('modelsTable');
    
    if (filteredModels.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = filteredModels.map(model => `
        <tr>
            <td>${model.id}</td>
            <td>${model.model_name}</td>
            <td><code>${model.model_uuid}</code></td>
            <td>${model.owner || '-'}</td>
            <td>
                <span class="badge ${getStatusBadgeClass(model.train_status)} status-badge">
                    ${getStatusText(model.train_status)}
                </span>
                ${model.error_reason ? `<br><small class="text-danger">${model.error_reason}</small>` : ''}
            </td>
            <td>${formatDate(model.created_time)}</td>
            <td>${formatDate(model.updated_time)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-info" onclick="viewModelDetails(${model.id})" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    ${model.train_status === 'completed' && model.download_url ? `
                        <a href="${model.download_url}" target="_black" class="btn btn-outline-success" download title="下载模型">
                            <i class="bi bi-download"></i>
                        </a>
                    ` : ''}
                    <button class="btn btn-outline-danger" onclick="deleteModel(${model.id})" title="删除模型">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function handleSearch() {
    searchTerm = document.getElementById('searchInput').value.toLowerCase();
    applyFilters();
}

function filterModels() {
    statusFilter = document.getElementById('statusFilter').value;
    applyFilters();
}

function applyFilters() {
    filteredModels = allModels.filter(model => {
        const matchesSearch = !searchTerm ||
            model.model_name.toLowerCase().includes(searchTerm) ||
            (model.owner && model.owner.toLowerCase().includes(searchTerm)) ||
            model.model_uuid.toLowerCase().includes(searchTerm);

        const matchesStatus = !statusFilter || model.train_status === statusFilter;

        return matchesSearch && matchesStatus;
    });

    renderModelsTable();
}

// 更新分页组件
function updatePagination() {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';

    if (totalPages <= 1) return; // 只有一页时不显示分页

    // 上一页按钮
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>`;
    pagination.appendChild(prevLi);

    // 页码按钮
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // 下一页按钮
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>`;
    pagination.appendChild(nextLi);
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages) return;
    loadModels(page);
}

function viewModelDetails(modelId) {
    const model = allModels.find(m => m.id === modelId);
    if (!model) return;
    
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <dl class="row">
                    <dt class="col-sm-4">模型ID</dt>
                    <dd class="col-sm-8">${model.id}</dd>
                    
                    <dt class="col-sm-4">模型名称</dt>
                    <dd class="col-sm-8">${model.model_name}</dd>
                    
                    <dt class="col-sm-4">模型UUID</dt>
                    <dd class="col-sm-8"><code>${model.model_uuid}</code></dd>
                    
                    <dt class="col-sm-4">所属用户</dt>
                    <dd class="col-sm-8">${model.owner || '-'}</dd>
                    
                    <dt class="col-sm-4">训练状态</dt>
                    <dd class="col-sm-8">
                        <span class="badge ${getStatusBadgeClass(model.train_status)} status-badge">
                            ${getStatusText(model.train_status)}
                        </span>
                    </dd>
                </dl>
            </div>
            <div class="col-md-6">
                <h6>时间信息</h6>
                <dl class="row">
                    <dt class="col-sm-4">创建时间</dt>
                    <dd class="col-sm-8">${formatDate(model.created_time)}</dd>
                    
                    <dt class="col-sm-4">更新时间</dt>
                    <dd class="col-sm-8">${formatDate(model.updated_time)}</dd>
                    
                    <dt class="col-sm-4">下载链接</dt>
                    <dd class="col-sm-8">
                        ${model.download_url ? 
                            `<a href="${model.download_url}" target="_black" class="btn btn-sm btn-outline-success" download>
                                <i class="bi bi-download"></i> 下载
                            </a>` : 
                            '<span class="text-muted">暂无</span>'
                        }
                    </dd>
                </dl>
            </div>
        </div>
        
        ${model.error_reason ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>错误信息</h6>
                    <div class="alert alert-danger">
                        ${model.error_reason}
                    </div>
                </div>
            </div>
        ` : ''}
    `;
    
    document.getElementById('modelDetailsContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('modelDetailsModal')).show();
}

async function deleteModel(modelId) {
    const model = allModels.find(m => m.id === modelId);
    if (!model) return;
    
    if (!confirm(`确定要删除模型 "${model.model_name}" 吗？\n\n删除后将无法恢复。`)) {
        return;
    }
    
    try {
        const response = await axios.delete(`/api/models/${modelId}`);
        if (response.data.code === 200) {
            showAlert('模型删除成功');
            loadModels();
        } else {
            showAlert(response.data.msg || '删除失败', 'danger');
        }
    } catch (error) {
        let message = '删除失败';
        if (error.response && error.response.data && error.response.data.detail) {
            message = error.response.data.detail;
        }
        showAlert(message, 'danger');
    }
}

// 刷新模型列表
async function refreshModels() {
    const refreshButton = document.querySelector('[onclick="refreshModels()"]');
    if (refreshButton) {
        refreshButton.disabled = true;
        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
    }

    try {
        await loadModels(currentPage, true); // 使用 no_cache=true 获取最新数据
        showAlert('模型列表已刷新', 'success');
    } catch (error) {
        showAlert('刷新模型列表失败', 'danger');
    } finally {
        if (refreshButton) {
            refreshButton.disabled = false;
            refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新';
        }
    }
}
</script>
{% endblock %}
