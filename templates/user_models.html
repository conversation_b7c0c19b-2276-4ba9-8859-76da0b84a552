{% extends "base.html" %}

{% block title %}我的模型 - HeyGem 管理系统{% endblock %}

{% block page_title %}我的模型{% endblock %}

{% block page_actions %}
<button type="button" class="btn btn-success me-2" onclick="refreshModels()">
    <i class="bi bi-arrow-clockwise"></i> 刷新
</button>
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createModelModal">
    <i class="bi bi-plus"></i> 新建模型
</button>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>模型名称</th>
                        <th>模型ID</th>
                        <th>训练状态</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="modelsTable">
                    <tr>
                        <td colspan="6" class="text-center">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页组件 -->
        <nav aria-label="模型列表分页" class="mt-3">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- Create Model Modal -->
<div class="modal fade" id="createModelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建模型</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createModelForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="modelName" class="form-label">模型名称</label>
                        <input type="text" class="form-control" id="modelName" name="model_name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Model Modal -->
<div class="modal fade" id="editModelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑模型</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editModelForm">
                <div class="modal-body">
                    <input type="hidden" id="editModelId" name="model_id">
                    <div class="mb-3">
                        <label for="editModelName" class="form-label">模型名称</label>
                        <input type="text" class="form-control" id="editModelName" name="model_name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Train Model Modal -->
<div class="modal fade" id="trainModelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">训练模型</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="trainModelForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="trainModelUuid" name="model_uuid">
                    <div class="mb-3">
                        <label for="audioFile" class="form-label">选择音频文件</label>
                        <input type="file" class="form-control" id="audioFile" name="audio_file" accept="audio/*" required>
                        <div class="form-text">支持 WAV, MP3, FLAC, AAC 格式</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-warning">开始训练</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let models = [];
let currentPage = 1;
const pageSize = 50;
let totalPages = 1;
let totalModels = 0;

document.addEventListener('DOMContentLoaded', function() {
    loadModels();

    // Create model form
    document.getElementById('createModelForm').addEventListener('submit', handleCreateModel);

    // Edit model form
    document.getElementById('editModelForm').addEventListener('submit', handleEditModel);

    // Train model form
    document.getElementById('trainModelForm').addEventListener('submit', handleTrainModel);

    // Refresh data every 30 seconds
    setInterval(() => loadModels(currentPage), 30000);
});

async function loadModels(page = 1, noCache = false) {
    try {
        const skip = (page - 1) * pageSize;
        let url = `/api/models?skip=${skip}&limit=${pageSize}`;
        if (noCache) {
            url += '&no_cache=true';
        }

        const response = await axios.get(url);
        if (response.data.code === 200) {
            models = response.data.data;
            if (response.data.pagination) {
                totalModels = response.data.pagination.total;
                totalPages = Math.ceil(totalModels / pageSize);
            }
            currentPage = page;
            renderModelsTable();
            updatePagination();
        }
    } catch (error) {
        console.error('Error loading models:', error);
        showAlert('加载模型列表失败', 'danger');
    }
}

function renderModelsTable() {
    const tbody = document.getElementById('modelsTable');
    
    if (models.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = models.map(model => `
        <tr>
            <td>${model.model_name}</td>
            <td><code>${model.model_uuid}</code></td>
            <td>
                <span class="badge ${getStatusBadgeClass(model.train_status)} status-badge">
                    ${getStatusText(model.train_status)}
                </span>
                ${model.error_reason ? `<br><small class="text-danger">${model.error_reason}</small>` : ''}
            </td>
            <td>${formatDate(model.created_time)}</td>
            <td>${formatDate(model.updated_time)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editModel(${model.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                    ${model.train_status === 'new' ? `
                        <button class="btn btn-outline-warning" onclick="trainModel('${model.model_uuid}')">
                            <i class="bi bi-play"></i>
                        </button>
                    ` : ''}
                    ${model.train_status === 'completed' && model.download_url ? `
                        <a href="${model.download_url}" target="_black" class="btn btn-outline-success" download>
                            <i class="bi bi-download"></i>
                        </a>
                    ` : ''}
                    <button class="btn btn-outline-danger" onclick="deleteModel(${model.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

async function handleCreateModel(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {
        model_name: formData.get('model_name')
    };

    try {
        const response = await axios.post('/api/models', data);

        if (response.data.code === 200) {
            showAlert('模型创建成功');
            bootstrap.Modal.getInstance(document.getElementById('createModelModal')).hide();
            e.target.reset();
            loadModels();
        } else {
            showAlert(response.data.msg || '创建失败', 'danger');
        }
    } catch (error) {
        let message = '创建失败';
        if (error.response && error.response.data && error.response.data.detail) {
            message = error.response.data.detail;
        }
        showAlert(message, 'danger');
    }
}

function editModel(modelId) {
    const model = models.find(m => m.id === modelId);
    if (model) {
        document.getElementById('editModelId').value = model.id;
        document.getElementById('editModelName').value = model.model_name;
        new bootstrap.Modal(document.getElementById('editModelModal')).show();
    }
}

async function handleEditModel(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const modelId = formData.get('model_id');
    const data = {
        model_name: formData.get('model_name')
    };
    
    try {
        const response = await axios.put(`/api/models/${modelId}`, data);
        if (response.data.code === 200) {
            showAlert('模型更新成功');
            bootstrap.Modal.getInstance(document.getElementById('editModelModal')).hide();
            loadModels();
        } else {
            showAlert(response.data.msg || '更新失败', 'danger');
        }
    } catch (error) {
        showAlert('更新失败', 'danger');
    }
}

function trainModel(modelUuid) {
    document.getElementById('trainModelUuid').value = modelUuid;
    new bootstrap.Modal(document.getElementById('trainModelModal')).show();
}

async function handleTrainModel(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    try {
        const response = await axios.post('/audio_train', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        
        if (response.data.code === 200) {
            showAlert('训练任务已提交');
            bootstrap.Modal.getInstance(document.getElementById('trainModelModal')).hide();
            e.target.reset();
            loadModels();
        } else {
            showAlert(response.data.msg || '训练提交失败', 'danger');
        }
    } catch (error) {
        showAlert('训练提交失败', 'danger');
    }
}

async function deleteModel(modelId) {
    if (!confirm('确定要删除这个模型吗？')) {
        return;
    }
    
    try {
        const response = await axios.delete(`/api/models/${modelId}`);
        if (response.data.code === 200) {
            showAlert('模型删除成功');
            loadModels();
        } else {
            showAlert(response.data.msg || '删除失败', 'danger');
        }
    } catch (error) {
        showAlert('删除失败', 'danger');
    }
}

// 更新分页组件
function updatePagination() {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';

    if (totalPages <= 1) return; // 只有一页时不显示分页

    // 上一页按钮
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>`;
    pagination.appendChild(prevLi);

    // 页码按钮
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // 下一页按钮
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>`;
    pagination.appendChild(nextLi);
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages) return;
    loadModels(page);
}

// 刷新模型列表
async function refreshModels() {
    const refreshButton = document.querySelector('[onclick="refreshModels()"]');
    if (refreshButton) {
        refreshButton.disabled = true;
        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
    }

    try {
        await loadModels(currentPage, true); // 使用 no_cache=true 获取最新数据
        showAlert('模型列表已刷新', 'success');
    } catch (error) {
        showAlert('刷新模型列表失败', 'danger');
    } finally {
        if (refreshButton) {
            refreshButton.disabled = false;
            refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新';
        }
    }
}
</script>
{% endblock %}
