{% extends "base.html" %}

{% block title %}视频形象管理（管理员）{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>视频形象管理（管理员）</h2>
            </div>

            <!-- Video Models Table -->
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索形象名称或用户...">
                                <button class="btn btn-outline-secondary" type="button" onclick="searchVideoModels()">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="statusFilter" onchange="filterVideoModels()">
                                <option value="">全部状态</option>
                                <option value="new">新建</option>
                                <option value="training">训练中</option>
                                <option value="completed">训练完成</option>
                                <option value="failed">训练失败</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-success" onclick="refreshVideoModels()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped" id="videoModelsTable">
                            <thead>
                                <tr>
                                    <th>形象名称</th>
                                    <th>所属用户</th>
                                    <th>序列号</th>
                                    <th>训练状态</th>
                                    <th>创建时间</th>
                                    <th>更新时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- Pagination will be generated by JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Video Model Modal -->
<div class="modal fade" id="editVideoModelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑视频形象</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editVideoModelForm">
                    <input type="hidden" id="editModelUuid" name="model_uuid">
                    <div class="mb-3">
                        <label for="editModelName" class="form-label">形象名称</label>
                        <input type="text" class="form-control" id="editModelName" name="model_name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateVideoModel()">保存</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
const pageSize = 50;
let totalPages = 1;
let allVideoModels = [];
let filteredVideoModels = [];
let totalVideoModels = 0;
let searchTerm = '';
let statusFilter = '';

// Load video models on page load
document.addEventListener('DOMContentLoaded', function() {
    loadVideoModels();

    // Search input event
    document.getElementById('searchInput').addEventListener('input', handleSearch);
});

// Load video models
async function loadVideoModels(page = 1, noCache = false) {
    try {
        const skip = (page - 1) * pageSize;
        let url = `/api/video_models?skip=${skip}&limit=${pageSize}`;
        if (noCache) {
            url += '&no_cache=true';
        }

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });

        if (!response.ok) {
            throw new Error('Failed to load video models');
        }

        const data = await response.json();
        allVideoModels = data.data || [];
        if (data.pagination) {
            totalVideoModels = data.pagination.total;
            totalPages = Math.ceil(totalVideoModels / pageSize);
        }
        currentPage = page;
        applyFilters();
        updatePagination();
    } catch (error) {
        console.error('Error loading video models:', error);
        showAlert('加载视频形象列表失败', 'danger');
    }
}

// Display video models in table
function displayVideoModels(videoModels) {
    const tbody = document.querySelector('#videoModelsTable tbody');
    tbody.innerHTML = '';
    
    videoModels.forEach(videoModel => {
        const row = document.createElement('tr');
        
        // Status badge
        let statusBadge = '';
        switch(videoModel.train_status) {
            case 'new':
                statusBadge = '<span class="badge bg-secondary">新建</span>';
                break;
            case 'training':
                statusBadge = '<span class="badge bg-warning">训练中</span>';
                break;
            case 'completed':
                statusBadge = '<span class="badge bg-success">训练完成</span>';
                break;
            case 'failed':
                statusBadge = '<span class="badge bg-danger">训练失败</span>';
                break;
        }
        
        // Actions
        let actions = '';
        if (videoModel.train_status === 'completed') {
            actions += `<a href="${videoModel.model_uuid}" target="_black" class="btn btn-sm btn-success me-1" target="_blank">下载</a>`;
        }
        actions += `<button class="btn btn-sm btn-primary me-1" onclick="showEditModal('${videoModel.model_uuid}', \`${videoModel.model_name.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`)">编辑</button>`;
        actions += `<button class="btn btn-sm btn-danger" onclick="deleteVideoModel('${videoModel.model_uuid}')">删除</button>`;
        
        row.innerHTML = `
            <td>${videoModel.model_name}</td>
            <td>${videoModel.owner || 'N/A'}</td>
            <td><code>${videoModel.model_uuid}</code></td>
            <td>${statusBadge}</td>
            <td>${new Date(videoModel.created_time).toLocaleString()}</td>
            <td>${new Date(videoModel.updated_time).toLocaleString()}</td>
            <td>${actions}</td>
        `;
        
        tbody.appendChild(row);
    });
}

function handleSearch() {
    searchTerm = document.getElementById('searchInput').value.toLowerCase();
    applyFilters();
}

function filterVideoModels() {
    statusFilter = document.getElementById('statusFilter').value;
    applyFilters();
}

function applyFilters() {
    filteredVideoModels = allVideoModels.filter(videoModel => {
        const matchesSearch = !searchTerm ||
            videoModel.model_name.toLowerCase().includes(searchTerm) ||
            (videoModel.owner && videoModel.owner.toLowerCase().includes(searchTerm)) ||
            videoModel.model_uuid.toLowerCase().includes(searchTerm);

        const matchesStatus = !statusFilter || videoModel.train_status === statusFilter;

        return matchesSearch && matchesStatus;
    });

    displayVideoModels(filteredVideoModels);
}

// Update pagination
function updatePagination() {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';

    if (totalPages <= 1) return; // Don't show pagination if only one page

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>`;
    pagination.appendChild(prevLi);

    // Page numbers
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>`;
    pagination.appendChild(nextLi);
}

// Change page
function changePage(page) {
    if (page < 1 || page > totalPages) return;
    loadVideoModels(page);
}

// Show edit modal
function showEditModal(modelUuid, modelName) {
    document.getElementById('editModelUuid').value = modelUuid;
    document.getElementById('editModelName').value = modelName;
    
    const modal = new bootstrap.Modal(document.getElementById('editVideoModelModal'));
    modal.show();
}

// Update video model
async function updateVideoModel() {
    const form = document.getElementById('editVideoModelForm');
    const formData = new FormData(form);
    const modelUuid = formData.get('model_uuid');
    
    try {
        const response = await fetch(`/api/video_models/${modelUuid}`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model_name: formData.get('model_name')
            })
        });
        
        if (!response.ok) {
            throw new Error('Failed to update video model');
        }
        
        showAlert('视频形象更新成功', 'success');
        
        // Close modal and reload data
        const modal = bootstrap.Modal.getInstance(document.getElementById('editVideoModelModal'));
        modal.hide();
        loadVideoModels(currentPage);
        
    } catch (error) {
        console.error('Error updating video model:', error);
        showAlert('更新视频形象失败', 'danger');
    }
}

// Delete video model
async function deleteVideoModel(modelUuid) {
    if (!confirm('确定要删除这个视频形象吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/video_models/${modelUuid}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to delete video model');
        }
        
        showAlert('视频形象删除成功', 'success');
        loadVideoModels(currentPage);
        
    } catch (error) {
        console.error('Error deleting video model:', error);
        showAlert('删除视频形象失败', 'danger');
    }
}

// Show alert
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));
    
    // Auto dismiss after 3 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// Refresh video models
async function refreshVideoModels() {
    const refreshButton = document.querySelector('[onclick="refreshVideoModels()"]');
    if (refreshButton) {
        refreshButton.disabled = true;
        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
    }

    try {
        await loadVideoModels(1, true); // 使用 no_cache=true 获取最新数据
        showAlert('视频形象列表已刷新', 'success');
    } catch (error) {
        showAlert('刷新视频形象列表失败', 'danger');
    } finally {
        if (refreshButton) {
            refreshButton.disabled = false;
            refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新';
        }
    }
}
</script>
{% endblock %}
